/* ===== CHATBOT IA STYLES ===== */

/* Container principal do chatbot */
.chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Botão flutuante para abrir o chatbot */
.chatbot-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.chatbot-toggle.active {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* Janela do chatbot */
.chatbot-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 400px;
    height: 600px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header do chatbot */
.chatbot-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.chatbot-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.chatbot-header .subtitle {
    font-size: 12px;
    opacity: 0.9;
    margin-top: 5px;
}

.chatbot-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.chatbot-close:hover {
    opacity: 1;
}

/* Área de mensagens */
.chatbot-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Mensagem individual */
.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    align-self: flex-end;
    margin-left: auto;
}

.message.bot {
    background: white;
    color: #333;
    align-self: flex-start;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message.bot .message-content {
    white-space: pre-wrap;
}

/* Indicador de digitação */
.typing-indicator {
    display: none;
    align-self: flex-start;
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    border: 1px solid #e9ecef;
    max-width: 80px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Área de input */
.chatbot-input-area {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chatbot-input-container {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.chatbot-input {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 12px 16px;
    font-size: 14px;
    resize: none;
    max-height: 100px;
    min-height: 44px;
    outline: none;
    transition: border-color 0.2s;
}

.chatbot-input:focus {
    border-color: #667eea;
}

.chatbot-send {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-send:hover:not(:disabled) {
    transform: scale(1.05);
}

.chatbot-send:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Sugestões de perguntas */
.chatbot-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.suggestion-chip {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    color: #667eea;
}

.suggestion-chip:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

/* Status do chatbot */
.chatbot-status {
    padding: 10px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.status-online {
    color: #28a745;
}

.status-offline {
    color: #dc3545;
}

/* Avaliação de resposta */
.message-rating {
    margin-top: 10px;
    display: flex;
    gap: 5px;
    align-items: center;
}

.rating-star {
    color: #ddd;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.2s;
}

.rating-star:hover,
.rating-star.active {
    color: #ffc107;
}

/* Responsividade */
@media (max-width: 768px) {
    .chatbot-window {
        width: 350px;
        height: 500px;
        bottom: 70px;
        right: -10px;
    }
    
    .chatbot-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .chatbot-window {
        width: calc(100vw - 40px);
        height: 70vh;
        bottom: 70px;
        right: 20px;
        left: 20px;
    }
}

/* Dark mode support */
[data-theme="dark"] .chatbot-window {
    background: #2d3748;
}

[data-theme="dark"] .chatbot-messages {
    background: #1a202c;
}

[data-theme="dark"] .message.bot {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
}

[data-theme="dark"] .chatbot-input-area {
    background: #2d3748;
    border-color: #4a5568;
}

[data-theme="dark"] .chatbot-input {
    background: #1a202c;
    color: #e2e8f0;
    border-color: #4a5568;
}

[data-theme="dark"] .suggestion-chip {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #718096;
}

[data-theme="dark"] .chatbot-status {
    background: #2d3748;
    border-color: #4a5568;
    color: #a0aec0;
}
