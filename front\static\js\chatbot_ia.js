/**
 * Chatbot IA - Sistema de Auditoria Fiscal
 * Interface inteligente para consultas sobre auditorias
 */

class ChatbotIA {
    constructor() {
        this.isOpen = false;
        this.isTyping = false;
        this.currentConversationId = null;
        this.suggestions = [];
        
        this.init();
    }
    
    init() {
        this.createChatbotHTML();
        this.bindEvents();
        this.loadSuggestions();
        this.checkStatus();
    }
    
    createChatbotHTML() {
        const chatbotHTML = `
            <div class="chatbot-container">
                <!-- Botão flutuante -->
                <button class="chatbot-toggle" id="chatbotToggle">
                    <i class="fas fa-robot"></i>
                </button>
                
                <!-- <PERSON><PERSON> do chatbot -->
                <div class="chatbot-window" id="chatbotWindow">
                    <!-- Header -->
                    <div class="chatbot-header">
                        <button class="chatbot-close" id="chatbotClose">
                            <i class="fas fa-times"></i>
                        </button>
                        <h3>Assistente IA</h3>
                        <div class="subtitle">Pergunte sobre suas auditorias</div>
                    </div>
                    
                    <!-- Mensagens -->
                    <div class="chatbot-messages" id="chatbotMessages">
                        <div class="message bot">
                            <div class="message-content">
                                Olá! 👋 Sou seu assistente de auditoria fiscal. 
                                Posso ajudar você com informações sobre:
                                
                                • Notas fiscais e inconsistências
                                • Status de cenários tributários  
                                • Relatórios de auditoria
                                • Estatísticas e resumos
                                
                                Como posso ajudar você hoje?
                            </div>
                        </div>
                    </div>
                    
                    <!-- Indicador de digitação -->
                    <div class="typing-indicator" id="typingIndicator">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                    
                    <!-- Área de input -->
                    <div class="chatbot-input-area">
                        <div class="chatbot-input-container">
                            <textarea 
                                class="chatbot-input" 
                                id="chatbotInput" 
                                placeholder="Digite sua pergunta..."
                                rows="1"
                            ></textarea>
                            <button class="chatbot-send" id="chatbotSend">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        
                        <!-- Sugestões -->
                        <div class="chatbot-suggestions" id="chatbotSuggestions"></div>
                    </div>
                    
                    <!-- Status -->
                    <div class="chatbot-status" id="chatbotStatus">
                        <span class="status-indicator">🟢 Online</span>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', chatbotHTML);
    }
    
    bindEvents() {
        const toggle = document.getElementById('chatbotToggle');
        const close = document.getElementById('chatbotClose');
        const input = document.getElementById('chatbotInput');
        const send = document.getElementById('chatbotSend');
        
        toggle.addEventListener('click', () => this.toggleChatbot());
        close.addEventListener('click', () => this.closeChatbot());
        send.addEventListener('click', () => this.sendMessage());
        
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        input.addEventListener('input', () => this.autoResize(input));
    }
    
    toggleChatbot() {
        const window = document.getElementById('chatbotWindow');
        const toggle = document.getElementById('chatbotToggle');
        
        if (this.isOpen) {
            this.closeChatbot();
        } else {
            window.style.display = 'flex';
            toggle.classList.add('active');
            this.isOpen = true;
            
            // Focar no input
            setTimeout(() => {
                document.getElementById('chatbotInput').focus();
            }, 300);
        }
    }
    
    closeChatbot() {
        const window = document.getElementById('chatbotWindow');
        const toggle = document.getElementById('chatbotToggle');
        
        window.style.display = 'none';
        toggle.classList.remove('active');
        this.isOpen = false;
    }
    
    async sendMessage() {
        const input = document.getElementById('chatbotInput');
        const message = input.value.trim();
        
        if (!message || this.isTyping) return;
        
        // Adicionar mensagem do usuário
        this.addMessage(message, 'user');
        input.value = '';
        this.autoResize(input);
        
        // Mostrar indicador de digitação
        this.showTyping();
        
        try {
            // Enviar para a API
            const response = await this.callAPI('/chatbot/pergunta', {
                pergunta: message,
                empresa_id: this.getCurrentEmpresaId()
            });
            
            if (response.success) {
                this.addMessage(response.resposta, 'bot', response);
                this.currentConversationId = response.conversa_id;
            } else {
                this.addMessage(
                    response.resposta || 'Desculpe, não consegui processar sua pergunta.',
                    'bot'
                );
            }
        } catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            this.addMessage(
                'Ops! Algo deu errado. Tente novamente em alguns instantes.',
                'bot'
            );
        } finally {
            this.hideTyping();
        }
    }
    
    addMessage(content, type, data = null) {
        const messagesContainer = document.getElementById('chatbotMessages');
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;
        
        messageDiv.appendChild(contentDiv);
        
        // Adicionar avaliação para mensagens do bot
        if (type === 'bot' && data && data.conversa_id) {
            const ratingDiv = this.createRatingElement(data.conversa_id);
            messageDiv.appendChild(ratingDiv);
        }
        
        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    createRatingElement(conversaId) {
        const ratingDiv = document.createElement('div');
        ratingDiv.className = 'message-rating';
        ratingDiv.innerHTML = `
            <span style="font-size: 12px; margin-right: 8px;">Útil?</span>
            ${[1,2,3,4,5].map(i => 
                `<span class="rating-star" data-rating="${i}" data-conversa="${conversaId}">⭐</span>`
            ).join('')}
        `;
        
        // Bind eventos de avaliação
        ratingDiv.querySelectorAll('.rating-star').forEach(star => {
            star.addEventListener('click', (e) => {
                const rating = parseInt(e.target.dataset.rating);
                const conversaId = e.target.dataset.conversa;
                this.rateMessage(conversaId, rating, ratingDiv);
            });
        });
        
        return ratingDiv;
    }
    
    async rateMessage(conversaId, rating, ratingElement) {
        try {
            await this.callAPI('/chatbot/avaliar', {
                conversa_id: conversaId,
                avaliacao: rating
            });
            
            // Atualizar visual
            const stars = ratingElement.querySelectorAll('.rating-star');
            stars.forEach((star, index) => {
                star.classList.toggle('active', index < rating);
            });
            
            // Mostrar agradecimento
            setTimeout(() => {
                ratingElement.innerHTML = '<span style="font-size: 12px; color: #28a745;">Obrigado pelo feedback!</span>';
            }, 1000);
            
        } catch (error) {
            console.error('Erro ao avaliar mensagem:', error);
        }
    }
    
    showTyping() {
        this.isTyping = true;
        const indicator = document.getElementById('typingIndicator');
        const send = document.getElementById('chatbotSend');
        
        indicator.style.display = 'block';
        send.disabled = true;
        this.scrollToBottom();
    }
    
    hideTyping() {
        this.isTyping = false;
        const indicator = document.getElementById('typingIndicator');
        const send = document.getElementById('chatbotSend');
        
        indicator.style.display = 'none';
        send.disabled = false;
    }
    
    scrollToBottom() {
        const messages = document.getElementById('chatbotMessages');
        setTimeout(() => {
            messages.scrollTop = messages.scrollHeight;
        }, 100);
    }
    
    autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
    }
    
    async loadSuggestions() {
        try {
            const response = await this.callAPI('/chatbot/sugestoes');
            if (response.success) {
                this.suggestions = response.sugestoes;
                this.renderSuggestions();
            }
        } catch (error) {
            console.error('Erro ao carregar sugestões:', error);
        }
    }
    
    renderSuggestions() {
        const container = document.getElementById('chatbotSuggestions');
        
        // Mostrar apenas 3 sugestões aleatórias
        const randomSuggestions = this.suggestions
            .sort(() => 0.5 - Math.random())
            .slice(0, 3);
        
        container.innerHTML = randomSuggestions
            .map(suggestion => 
                `<div class="suggestion-chip" onclick="chatbot.useSuggestion('${suggestion}')">${suggestion}</div>`
            ).join('');
    }
    
    useSuggestion(suggestion) {
        const input = document.getElementById('chatbotInput');
        input.value = suggestion;
        input.focus();
        this.autoResize(input);
    }
    
    async checkStatus() {
        try {
            const response = await this.callAPI('/chatbot/status');
            const statusElement = document.getElementById('chatbotStatus');
            
            if (response.success) {
                statusElement.innerHTML = '<span class="status-online">🟢 Online</span>';
            } else {
                statusElement.innerHTML = '<span class="status-offline">🔴 Offline</span>';
            }
        } catch (error) {
            const statusElement = document.getElementById('chatbotStatus');
            statusElement.innerHTML = '<span class="status-offline">🔴 Offline</span>';
        }
    }
    
    getCurrentEmpresaId() {
        // Tentar obter empresa_id do contexto atual
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('empresa_id') || null;
    }
    
    async callAPI(endpoint, data = null) {
        const token = localStorage.getItem('token');
        
        const options = {
            method: data ? 'POST' : 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(endpoint, options);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.json();
    }
}

// Inicializar chatbot quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    // Verificar se o usuário está logado
    const token = localStorage.getItem('token');
    if (token) {
        window.chatbot = new ChatbotIA();
    }
});
